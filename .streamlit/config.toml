[global]
# Global configuration for Streamlit app

# Development mode
developmentMode = false

# Show warning for potential security issues
showWarningOnDirectExecution = false

[server]
# Server configuration
port = 8501
address = "localhost"

# Enable CORS for development
enableCORS = true
enableXsrfProtection = true

# File upload settings
maxUploadSize = 50
maxMessageSize = 50

# Session state
enableStaticServing = true

[browser]
# Browser configuration
gatherUsageStats = false
serverAddress = "localhost"
serverPort = 8501

[theme]
# Custom theme configuration
primaryColor = "#667eea"
backgroundColor = "#ffffff"
secondaryBackgroundColor = "#f8fafc"
textColor = "#1f2937"
font = "sans serif"

[client]
# Client configuration
caching = true
displayEnabled = true
showErrorDetails = true

[runner]
# Runner configuration
magicEnabled = true
installTracer = false
fixMatplotlib = true

[logger]
# Logging configuration
level = "info"
messageFormat = "%(asctime)s %(message)s"

[deprecation]
# Deprecation warnings
showPyplotGlobalUse = false
