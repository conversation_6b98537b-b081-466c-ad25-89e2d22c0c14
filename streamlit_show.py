import streamlit as st
import pandas as pd
import os
import time
import json
from typing import Optional, List
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from Constants.Config import settings
from InfoExtractor.policy_scrapper import process_policy_docs
from InfoExtractor.multi_file_processor import process_multiple_files
from utils.LogUtils import logger
from utils.DataUtils import clear_folder


# --- Page Configuration ---
st.set_page_config(
    page_title="Insurance Policy Scrapper | AI-Powered Document Analysis",
    page_icon="🛡️",
    layout="wide",
    initial_sidebar_state="collapsed",
    menu_items={
        'Get Help': 'https://github.com/your-repo/help',
        'Report a bug': 'https://github.com/your-repo/issues',
        'About': "# Insurance Policy Scrapper\nAI-powered document analysis for insurance policies."
    }
)


# --- Session State Initialization ---
def initialize_session_state():
    """Initialize all session state variables"""
    session_vars = {
        'pdf_uploaded': False,
        'excel_path': None,
        'pdf_path': None,
        'processing': False,
        'processing_complete': False,
        'error_message': None,
        'json_result': None,
        'upload_success': False,
        'processing_start_time': None,
        'processing_history': [],
        'processing_stats': {},
        'show_reset_confirmation': False,
        'reset_in_progress': False,
        # Multi-file support
        'uploaded_files': [],
        'multi_file_mode': False,
        'file_processing_results': {},
        'overall_progress': 0,
        'files_completed': 0,
        'total_files': 0,
        'multi_processing_complete': False,
        'combined_results': None
    }

    for var, default_value in session_vars.items():
        if var not in st.session_state:
            st.session_state[var] = default_value

initialize_session_state()

# --- Utility Functions ---
def get_file_size_mb(file_size_bytes):
    """Convert bytes to MB with formatting"""
    return round(file_size_bytes / (1024 * 1024), 2)

def format_processing_time(seconds):
    """Format processing time in a human-readable way"""
    if seconds < 60:
        return f"{seconds:.1f} seconds"
    elif seconds < 3600:
        minutes = seconds // 60
        remaining_seconds = seconds % 60
        return f"{int(minutes)}m {remaining_seconds:.0f}s"
    else:
        hours = seconds // 3600
        remaining_minutes = (seconds % 3600) // 60
        return f"{int(hours)}h {int(remaining_minutes)}m"



def add_to_processing_history(filename, status, processing_time=None, error=None):
    """Add entry to processing history"""
    entry = {
        'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        'filename': filename,
        'status': status,
        'processing_time': processing_time,
        'error': error
    }

    if 'processing_history' not in st.session_state:
        st.session_state.processing_history = []

    st.session_state.processing_history.insert(0, entry)  # Add to beginning

    # Keep only last 10 entries
    if len(st.session_state.processing_history) > 10:
        st.session_state.processing_history = st.session_state.processing_history[:10]

# --- Enhanced Helper Functions ---
def validate_pdf_file(uploaded_file) -> tuple[bool, str, dict]:
    """Validate uploaded PDF file with detailed information"""
    if uploaded_file is None:
        return False, "No file uploaded", {}

    file_info = {
        'name': uploaded_file.name,
        'size_bytes': uploaded_file.size,
        'size_mb': get_file_size_mb(uploaded_file.size),
        'type': uploaded_file.type
    }

    if not uploaded_file.name.lower().endswith('.pdf'):
        return False, "❌ Please upload a PDF file only", file_info

    if uploaded_file.size > 50 * 1024 * 1024:  # 50MB limit
        return False, f"❌ File size too large ({file_info['size_mb']} MB). Please upload a file smaller than 50MB", file_info

    if uploaded_file.size < 1024:  # Less than 1KB
        return False, "❌ File appears to be too small or corrupted", file_info

    return True, f"✅ File is valid ({file_info['size_mb']} MB)", file_info


def validate_multiple_pdf_files(uploaded_files) -> tuple[bool, str, list]:
    """Validate multiple uploaded PDF files"""
    if not uploaded_files:
        return False, "No files uploaded", []

    if len(uploaded_files) > 10:
        return False, "❌ Too many files. Please upload a maximum of 10 files", []

    valid_files = []
    invalid_files = []
    total_size = 0

    for uploaded_file in uploaded_files:
        is_valid, message, file_info = validate_pdf_file(uploaded_file)
        if is_valid:
            valid_files.append(file_info)
            total_size += file_info['size_bytes']
        else:
            invalid_files.append({'name': uploaded_file.name, 'error': message})

    # Check total size (500MB limit for all files combined)
    if total_size > 500 * 1024 * 1024:
        return False, f"❌ Total file size too large ({get_file_size_mb(total_size)} MB). Please keep total size under 500MB", []

    if invalid_files:
        error_msg = "❌ Some files are invalid:\n" + "\n".join([f"• {f['name']}: {f['error']}" for f in invalid_files])
        return False, error_msg, valid_files

    return True, f"✅ All {len(valid_files)} files are valid (Total: {get_file_size_mb(total_size)} MB)", valid_files

def save_uploaded_file(uploaded_file) -> tuple[bool, str, Optional[str]]:
    """Save uploaded file to the designated folder with enhanced feedback"""
    try:
        # Ensure the root folder exists
        os.makedirs(settings.root_folder, exist_ok=True)

        # Clear old files from the folder before uploading new one
        try:
            logger.info(f"Clearing old files from folder: {settings.root_folder}")
            clear_folder(settings.root_folder)
            logger.info("Old files cleared successfully")
        except Exception as clear_error:
            logger.warning(f"Warning: Could not clear old files: {clear_error}")
            # Continue with upload even if clearing fails

        filename = uploaded_file.name
        save_path = os.path.join(settings.root_folder, filename)

        # Safety check for existing files (though folder was cleared)
        counter = 1
        original_path = save_path
        while os.path.exists(save_path):
            name, ext = os.path.splitext(original_path)
            save_path = f"{name}_{counter}{ext}"
            counter += 1

        # Reset file pointer to beginning
        uploaded_file.seek(0)

        with open(save_path, "wb") as f:
            f.write(uploaded_file.read())

        logger.info(f"File saved successfully: {save_path}")
        return True, f"✅ File saved as: {os.path.basename(save_path)}", save_path

    except Exception as e:
        logger.error(f"Error saving file: {e}")
        return False, f"❌ Error saving file: {str(e)}", None


def save_multiple_uploaded_files(uploaded_files) -> tuple[bool, str, List[str]]:
    """Save multiple uploaded files to the designated folder"""
    try:
        # Ensure the root folder exists
        os.makedirs(settings.root_folder, exist_ok=True)

        # Clear old files from the folder before uploading new ones
        try:
            logger.info(f"Clearing old files from folder: {settings.root_folder}")
            clear_folder(settings.root_folder)
            logger.info("Old files cleared successfully")
        except Exception as clear_error:
            logger.warning(f"Warning: Could not clear old files: {clear_error}")

        saved_paths = []
        failed_files = []

        for uploaded_file in uploaded_files:
            try:
                filename = uploaded_file.name
                save_path = os.path.join(settings.root_folder, filename)

                # Handle duplicate filenames
                counter = 1
                original_path = save_path
                while os.path.exists(save_path):
                    name, ext = os.path.splitext(original_path)
                    save_path = f"{name}_{counter}{ext}"
                    counter += 1

                # Reset file pointer to beginning
                uploaded_file.seek(0)

                with open(save_path, "wb") as f:
                    f.write(uploaded_file.read())

                saved_paths.append(save_path)
                logger.info(f"File saved successfully: {save_path}")

            except Exception as e:
                failed_files.append(f"{uploaded_file.name}: {str(e)}")
                logger.error(f"Error saving file {uploaded_file.name}: {e}")

        if failed_files:
            error_msg = f"❌ Some files failed to save:\n" + "\n".join([f"• {f}" for f in failed_files])
            return False, error_msg, saved_paths

        return True, f"✅ All {len(saved_paths)} files saved successfully", saved_paths

    except Exception as e:
        logger.error(f"Error saving multiple files: {e}")
        return False, f"❌ Error saving files: {str(e)}", []

def reset_session():
    """Reset all session state variables and clear old files"""
    st.session_state.reset_in_progress = True

    # Clear old files from the folder
    try:
        if os.path.exists(settings.root_folder):
            logger.info(f"Clearing files from folder during session reset: {settings.root_folder}")
            clear_folder(settings.root_folder)
            logger.info("Files cleared successfully during session reset")
    except Exception as clear_error:
        logger.warning(f"Warning: Could not clear files during session reset: {clear_error}")
        st.session_state.error_message = f"Warning: Could not clear old files: {clear_error}"

    # Reset session state variables
    st.session_state.pdf_uploaded = False
    st.session_state.excel_path = None
    st.session_state.pdf_path = None
    st.session_state.processing = False
    st.session_state.processing_complete = False
    st.session_state.error_message = None
    st.session_state.json_result = None
    st.session_state.upload_success = False
    st.session_state.processing_start_time = None
    st.session_state.processing_stats = {}
    st.session_state.show_reset_confirmation = False
    st.session_state.reset_in_progress = False

    # Reset multi-file specific variables
    st.session_state.uploaded_files = []
    st.session_state.multi_file_mode = False
    st.session_state.file_processing_results = {}
    st.session_state.overall_progress = 0
    st.session_state.files_completed = 0
    st.session_state.total_files = 0
    st.session_state.multi_processing_complete = False
    st.session_state.combined_results = None





# --- Main Application ---
def main():
    """Enhanced main application function"""

    # --- Header with enhanced styling ---
    st.markdown("""
    <div style="text-align: center; margin-bottom: 2rem;">
        <h1 class="main-header">🛡️ Insurance Policy Scrapper</h1>
        <p style="font-size: 1.2rem; color: #64748b; margin-top: -1rem;">
            AI-Powered Document Data Extraction
        </p>
        <div style="width: 100px; height: 4px; background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); margin: 1rem auto; border-radius: 2px;"></div>
    </div>
    """, unsafe_allow_html=True)



    # --- Error Display ---
    if st.session_state.error_message:
        st.error(st.session_state.error_message)

        # Add error recovery suggestions
        st.markdown("#### 🔧 Troubleshooting Tips")
        st.info("""
        - Ensure your PDF file is not corrupted
        - Check that the file size is under 50MB
        - Try uploading a different PDF file
        - Refresh the page and try again
        """)

    # --- Upload Section ---
    st.markdown("### 📁 Upload Policy Documents")

    # File upload mode selection
    col1, col2 = st.columns(2)
    with col1:
        upload_mode = st.radio(
            "Upload Mode:",
            ["Single File", "Multiple Files"],
            horizontal=True,
            help="Choose whether to upload one file or multiple files for batch processing"
        )

    st.session_state.multi_file_mode = (upload_mode == "Multiple Files")

    if st.session_state.multi_file_mode:
        st.markdown("*Drag and drop your PDF files or click to browse (up to 10 files)*")
        uploaded_files = st.file_uploader(
            "Choose PDF files",
            type="pdf",
            accept_multiple_files=True,
            help="Upload your insurance policy documents in PDF format (Max: 50MB each, up to 10 files)",
            label_visibility="collapsed"
        )
    else:
        st.markdown("*Drag and drop your PDF file or click to browse*")
        uploaded_file = st.file_uploader(
            "Choose a PDF file",
            type="pdf",
            help="Upload your insurance policy document in PDF format (Max: 50MB)",
            label_visibility="collapsed"
        )

    # Handle file uploads based on mode
    if st.session_state.multi_file_mode:
        if uploaded_files:
            # Enhanced validation for multiple files
            is_valid, message, file_infos = validate_multiple_pdf_files(uploaded_files)

            if is_valid:
                if not st.session_state.upload_success:
                    # Save files with progress indication
                    with st.spinner("💾 Saving files..."):
                        success, save_message, save_paths = save_multiple_uploaded_files(uploaded_files)

                    if success:
                        st.session_state.uploaded_files = save_paths
                        st.session_state.pdf_uploaded = True
                        st.session_state.upload_success = True
                        st.session_state.error_message = None
                        st.session_state.total_files = len(save_paths)
                        st.success(save_message)

                        # Display file summary
                        st.markdown("#### 📋 Uploaded Files Summary")
                        for i, file_info in enumerate(file_infos):
                            st.write(f"**{i+1}.** {file_info['name']} ({file_info['size_mb']} MB)")
                    else:
                        st.session_state.error_message = save_message
                        st.rerun()
                else:
                    st.success(f"✅ {len(uploaded_files)} files ready for processing")

                    # Display file summary
                    st.markdown("#### 📋 Uploaded Files Summary")
                    for i, uploaded_file in enumerate(uploaded_files):
                        st.write(f"**{i+1}.** {uploaded_file.name} ({get_file_size_mb(uploaded_file.size)} MB)")
            else:
                st.session_state.error_message = message
                st.rerun()
    else:
        if uploaded_file:
            # Enhanced validation with file info
            is_valid, message, file_info = validate_pdf_file(uploaded_file)

            if is_valid:
                if not st.session_state.upload_success:
                    # Save file with progress indication
                    with st.spinner("💾 Saving file..."):
                        success, save_message, save_path = save_uploaded_file(uploaded_file)

                    if success:
                        st.session_state.pdf_path = save_path
                        st.session_state.uploaded_files = [save_path]  # For consistency
                        st.session_state.pdf_uploaded = True
                        st.session_state.upload_success = True
                        st.session_state.error_message = None
                        st.session_state.total_files = 1
                        st.success(save_message)
                    else:
                        st.session_state.error_message = save_message
                        st.rerun()
                else:
                    st.success(f"✅ File ready: {uploaded_file.name}")
            else:
                st.session_state.error_message = message
                st.rerun()



    # --- Enhanced Processing Section ---
    if st.session_state.pdf_uploaded and not (st.session_state.processing_complete or st.session_state.multi_processing_complete):
        st.markdown("---")
        if st.session_state.multi_file_mode:
            st.markdown("### 🔄 Multi-Document Processing")
            st.info(f"📊 Ready to process {st.session_state.total_files} documents with 5 concurrent threads")
        else:
            st.markdown("### 🔄 Document Processing")

        col1, col2, col3 = st.columns([1, 2, 1])

        with col2:
            if not st.session_state.processing:
                # Enhanced process button with confirmation
                button_text = "🚀 Start Batch Processing" if st.session_state.multi_file_mode else "🚀 Start Processing"
                help_text = "Begin extracting data from your policy documents using 5 concurrent threads" if st.session_state.multi_file_mode else "Begin extracting data from your policy document"

                if st.button(button_text, type="primary", use_container_width=True, help=help_text):
                    st.session_state.processing = True
                    st.session_state.processing_start_time = time.time()
                    st.session_state.error_message = None
                    st.session_state.files_completed = 0
                    st.session_state.file_processing_results = {}
                    st.rerun()
            else:
                # Processing display
                if st.session_state.multi_file_mode:
                    st.info("⏳ Processing documents concurrently... Please wait.")

                    # Multi-file progress tracking
                    progress_container = st.container()
                    with progress_container:
                        overall_progress = st.progress(0)
                        status_text = st.empty()
                        time_text = st.empty()

                        # Individual file progress
                        file_progress_container = st.container()

                    # Process multiple files
                    def progress_callback(filename, status, progress):
                        st.session_state.file_processing_results[filename] = {
                            'status': status,
                            'progress': progress
                        }
                        if status in ['success', 'failed']:
                            st.session_state.files_completed += 1

                        # Update overall progress
                        st.session_state.overall_progress = (st.session_state.files_completed / st.session_state.total_files) * 100

                    try:
                        start_time = st.session_state.processing_start_time or time.time()

                        # Use the multi-file processor
                        multi_result = process_multiple_files(
                            st.session_state.uploaded_files,
                            max_workers=5,
                            progress_callback=progress_callback
                        )

                        # Calculate processing time
                        processing_time = time.time() - start_time

                        # Update session state with results
                        st.session_state.combined_results = multi_result
                        st.session_state.processing = False
                        st.session_state.multi_processing_complete = True

                        # Store processing statistics
                        st.session_state.processing_stats = {
                            'processing_time': format_processing_time(processing_time),
                            'total_files': multi_result.total_files,
                            'successful_files': multi_result.successful_files,
                            'failed_files': multi_result.failed_files
                        }

                        overall_progress.progress(100)
                        status_text.text("✅ Multi-file processing complete!")
                        time_text.text(f"⏱️ Total time: {format_processing_time(processing_time)}")

                        st.success(f"🎉 Processed {multi_result.successful_files}/{multi_result.total_files} documents successfully in {format_processing_time(processing_time)}!")
                        st.rerun()

                    except Exception as e:
                        processing_time = time.time() - start_time if st.session_state.processing_start_time else 0
                        logger.error(f"Multi-file processing error: {e}")

                        st.session_state.error_message = f"❌ Multi-file processing failed: {str(e)}"
                        st.session_state.processing = False
                        st.rerun()

                else:
                    # Single file processing (existing logic)
                    st.info("⏳ Processing document... Please wait.")

                    # Enhanced progress tracking
                    progress_container = st.container()
                    with progress_container:
                        progress_bar = st.progress(0)
                        status_text = st.empty()
                        time_text = st.empty()

                    try:
                        start_time = st.session_state.processing_start_time or time.time()
                        result = process_policy_docs(settings.root_folder, False)

                        # Calculate processing time
                        processing_time = time.time() - start_time

                        # Update session state with results and stats
                        st.session_state.json_result = result['result']
                        st.session_state.excel_path = result['output_file_path']
                        st.session_state.processing = False
                        st.session_state.processing_complete = True

                        # Store processing statistics
                        st.session_state.processing_stats = {
                            'processing_time': format_processing_time(processing_time),
                            'fields_extracted': len(result.get('result', {})) if result.get('result') else 0
                        }

                        # Add to processing history
                        filename = os.path.basename(st.session_state.pdf_path) if st.session_state.pdf_path else 'Unknown'
                        add_to_processing_history(filename, 'success', format_processing_time(processing_time))

                        progress_bar.progress(100)
                        status_text.text("✅ Processing complete!")
                        time_text.text(f"⏱️ Total time: {format_processing_time(processing_time)}")

                        st.success(f"🎉 Document processed successfully in {format_processing_time(processing_time)}!")
                        st.rerun()

                    except Exception as e:
                        processing_time = time.time() - start_time if st.session_state.processing_start_time else 0
                        logger.error(f"Processing error: {e}")

                        # Add to processing history
                        filename = os.path.basename(st.session_state.pdf_path) if st.session_state.pdf_path else 'Unknown'
                        add_to_processing_history(filename, 'error', format_processing_time(processing_time), str(e))

                        st.session_state.error_message = f"❌ Processing failed: {str(e)}"
                        st.session_state.processing = False
                        st.rerun()

    # --- Enhanced Results Section ---
    if (st.session_state.processing_complete and st.session_state.excel_path) or (st.session_state.multi_processing_complete and st.session_state.combined_results):
        st.markdown("---")

        if st.session_state.multi_processing_complete:
            st.markdown("### 📊 Multi-File Processing Results")

            # Multi-file results summary
            multi_result = st.session_state.combined_results

            # Enhanced metrics dashboard for multi-file
            st.markdown("#### 📈 Processing Summary")
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric(
                    "📁 Total Files",
                    multi_result.total_files,
                    help="Total number of files processed"
                )
            with col2:
                st.metric(
                    "✅ Successful",
                    multi_result.successful_files,
                    help="Number of files processed successfully"
                )
            with col3:
                st.metric(
                    "❌ Failed",
                    multi_result.failed_files,
                    help="Number of files that failed to process"
                )
            with col4:
                st.metric(
                    "⏱️ Total Time",
                    format_processing_time(multi_result.total_processing_time),
                    help="Total time taken to process all files"
                )

            # Individual file results
            st.markdown("#### 📋 Individual File Results")

            # Create tabs for successful and failed files
            if multi_result.successful_files > 0 and multi_result.failed_files > 0:
                tab1, tab2 = st.tabs(["✅ Successful Files", "❌ Failed Files"])
            elif multi_result.successful_files > 0:
                tab1 = st.tabs(["✅ Successful Files"])[0]
                tab2 = None
            else:
                tab1 = None
                tab2 = st.tabs(["❌ Failed Files"])[0]

            # Display successful files
            if tab1 and multi_result.successful_files > 0:
                with tab1:
                    for result in multi_result.results:
                        if result.status == 'success':
                            with st.expander(f"📄 {result.filename} - ✅ Success ({format_processing_time(result.processing_time or 0)})"):
                                if result.result and result.result.get('output_file_path'):
                                    try:
                                        df = pd.read_excel(result.result['output_file_path'])
                                        st.dataframe(df, use_container_width=True, hide_index=True)

                                        # Download button for individual file
                                        with open(result.result['output_file_path'], "rb") as file:
                                            st.download_button(
                                                label=f"📥 Download {result.filename} Results",
                                                data=file.read(),
                                                file_name=f"{os.path.splitext(result.filename)[0]}_results.xlsx",
                                                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                                            )
                                    except Exception as e:
                                        st.error(f"Error loading results for {result.filename}: {str(e)}")
                                else:
                                    st.warning("No detailed results available for this file")

            # Display failed files
            if tab2 and multi_result.failed_files > 0:
                with tab2:
                    for result in multi_result.results:
                        if result.status == 'failed':
                            with st.expander(f"📄 {result.filename} - ❌ Failed"):
                                st.error(f"Error: {result.error_message}")
                                if result.processing_time:
                                    st.write(f"Processing time: {format_processing_time(result.processing_time)}")

        else:
            # Single file results (existing logic)
            st.markdown("### 📊 Extraction Results")

            try:
                # Load and display Excel data
                df = pd.read_excel(st.session_state.excel_path)

                # Enhanced metrics dashboard
                st.markdown("#### 📈 Processing Summary")
                col1, col2, col3 = st.columns(3)

                with col1:
                    st.metric(
                        "📋 Fields Extracted",
                        len(df),
                        help="Total number of data fields extracted from the document"
                    )
                with col2:
                    st.metric(
                        "⏱️ Processing Time",
                        st.session_state.processing_stats.get('processing_time', 'N/A'),
                        help="Time taken to process the document"
                    )
                with col3:
                    st.metric(
                        "📄 Document Status",
                        "✅ Processed",
                        help="Current status of the document processing"
                    )

                # Enhanced data display with filtering
                st.markdown("#### 📋 Extracted Data")

                # Add search and filter options
                col1, col2 = st.columns([2, 1])
                with col1:
                    search_term = st.text_input("🔍 Search fields", placeholder="Type to search...")

                # Filter dataframe based on search and empty field settings
                display_df = df.copy()
                if search_term:
                    mask = display_df.iloc[:, 0].str.contains(search_term, case=False, na=False)
                    display_df = display_df[mask]

                # Display filtered data with enhanced styling
                st.dataframe(
                    display_df,
                    use_container_width=True,
                    hide_index=True,
                    column_config={
                        display_df.columns[0]: st.column_config.TextColumn("Field Name", width="medium"),
                        display_df.columns[1]: st.column_config.TextColumn("Extracted Value", width="large")
                    }
                )

                # Data export section
                st.markdown("#### 💾 Download & Export Options")

                col1, col2 = st.columns(2)

                with col1:
                    # Excel download with timestamp
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    with open(st.session_state.excel_path, "rb") as file:
                        st.download_button(
                            label="📥 Download Excel",
                            data=file.read(),
                            file_name=f"policy_extraction_{timestamp}.xlsx",
                            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                            type="primary",
                            help="Download results as Excel file"
                        )

                with col2:
                    # JSON download
                    if st.session_state.json_result:
                        json_str = json.dumps(st.session_state.json_result, indent=2)
                        st.download_button(
                            label="📥 Download JSON",
                            data=json_str,
                            file_name=f"policy_extraction_{timestamp}.json",
                            mime="application/json",
                            help="Download raw data as JSON file"
                        )

            except Exception as e:
                logger.error(f"Error displaying results: {e}")
                st.error(f"❌ Error loading results: {str(e)}")

                # Error recovery option
                if st.button("🔄 Retry Loading Results"):
                    st.rerun()

    # --- Enhanced Reset Section ---
    if st.session_state.pdf_uploaded or st.session_state.processing_complete or st.session_state.multi_processing_complete:
        st.markdown("---")
        st.markdown("### 🔄 Next Steps")

        # Show reset in progress state
        if st.session_state.reset_in_progress:
            st.info("🔄 Resetting session... Please wait.")
            return

        # Handle reset confirmation state
        if st.session_state.show_reset_confirmation:
            st.warning("⚠️ This will clear all current data. Are you sure?")

            col_a, col_b, col_c = st.columns([1, 1, 1])

            with col_a:
                if st.button("✅ Yes, Clear Session", type="primary", key="confirm_reset"):
                    with st.spinner("🔄 Clearing session..."):
                        reset_session()
                    st.success("✅ Session cleared! You can now upload a new document.")
                    time.sleep(1)
                    st.rerun()

            with col_b:
                if st.button("❌ Cancel", type="secondary", key="cancel_reset"):
                    st.session_state.show_reset_confirmation = False
                    st.rerun()
        else:
            col1, col2, col3 = st.columns([1, 2, 1])

            with col2:
                if st.button("🔄 Process New Document", type="secondary", use_container_width=True,
                            help="Clear current session and start fresh", key="process_new_doc"):
                    st.session_state.show_reset_confirmation = True
                    st.rerun()

    # --- Footer ---
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #64748b; padding: 2rem 0;">
        <p>🛡️ <strong>Insurance Policy Scrapper</strong> | Powered by AI</p>
        <p style="font-size: 0.9rem;">
            Built with ❤️ using Streamlit |
            <a href="#" style="color: #667eea; text-decoration: none;">Documentation</a> |
            <a href="#" style="color: #667eea; text-decoration: none;">Support</a> |
            <a href="#" style="color: #667eea; text-decoration: none;">GitHub</a>
        </p>
    </div>
    """, unsafe_allow_html=True)


if __name__ == "__main__":
    main()
