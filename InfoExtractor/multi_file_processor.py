"""
Multi-file processing module for concurrent policy document processing.
Handles multiple PDF files with threading support for improved performance.
"""

import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass
from datetime import datetime

from utils.LogUtils import logger
from InfoExtractor.policy_scrapper import process_single_policy_doc
from utils.DataUtils import clear_folder


@dataclass
class FileProcessingResult:
    """Result of processing a single file."""
    filename: str
    file_path: str
    status: str  # 'success', 'failed', 'processing'
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    processing_time: Optional[float] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None


@dataclass
class MultiFileProcessingResult:
    """Result of processing multiple files."""
    total_files: int
    successful_files: int
    failed_files: int
    total_processing_time: float
    results: List[FileProcessingResult]
    combined_output_path: Optional[str] = None


class MultiFileProcessor:
    """Handles concurrent processing of multiple policy documents."""
    
    def __init__(self, max_workers: int = 5):
        """
        Initialize the multi-file processor.
        
        Args:
            max_workers: Maximum number of concurrent threads (default: 5)
        """
        self.max_workers = max_workers
        self.processing_lock = threading.Lock()
        self.results_lock = threading.Lock()
        
    def process_single_file_wrapper(self, file_path: str, progress_callback: Optional[Callable] = None) -> FileProcessingResult:
        """
        Wrapper function to process a single file with error handling and timing.
        
        Args:
            file_path: Path to the PDF file to process
            progress_callback: Optional callback function for progress updates
            
        Returns:
            FileProcessingResult object with processing results
        """
        filename = os.path.basename(file_path)
        start_time = datetime.now()
        
        result = FileProcessingResult(
            filename=filename,
            file_path=file_path,
            status='processing',
            start_time=start_time
        )
        
        try:
            logger.info(f"Starting processing of file: {filename}")
            
            if progress_callback:
                progress_callback(filename, 'processing', 0)
            
            # Create a temporary folder for this file
            temp_folder = os.path.join(os.path.dirname(file_path), f"temp_{int(time.time())}_{threading.get_ident()}")
            os.makedirs(temp_folder, exist_ok=True)
            
            # Copy file to temp folder to avoid conflicts
            import shutil
            temp_file_path = os.path.join(temp_folder, filename)
            shutil.copy2(file_path, temp_file_path)
            
            # Process the file
            processing_result = process_single_policy_doc(temp_file_path, send_pdf=False)
            
            if processing_result and processing_result.get('status') != 'Failed':
                result.status = 'success'
                result.result = processing_result
                logger.info(f"Successfully processed file: {filename}")
                
                if progress_callback:
                    progress_callback(filename, 'success', 100)
            else:
                result.status = 'failed'
                result.error_message = "Processing returned failed status or no result"
                logger.error(f"Failed to process file: {filename}")
                
                if progress_callback:
                    progress_callback(filename, 'failed', 100)
            
            # Clean up temp folder
            try:
                shutil.rmtree(temp_folder)
            except Exception as cleanup_error:
                logger.warning(f"Could not clean up temp folder {temp_folder}: {cleanup_error}")
                
        except Exception as e:
            result.status = 'failed'
            result.error_message = str(e)
            logger.error(f"Error processing file {filename}: {e}")
            
            if progress_callback:
                progress_callback(filename, 'failed', 100)
        
        finally:
            end_time = datetime.now()
            result.end_time = end_time
            result.processing_time = (end_time - start_time).total_seconds()
            
        return result
    
    def process_multiple_files(self, 
                             file_paths: List[str], 
                             progress_callback: Optional[Callable] = None) -> MultiFileProcessingResult:
        """
        Process multiple files concurrently using ThreadPoolExecutor.
        
        Args:
            file_paths: List of file paths to process
            progress_callback: Optional callback function for progress updates
            
        Returns:
            MultiFileProcessingResult object with aggregated results
        """
        start_time = time.time()
        results = []
        
        logger.info(f"Starting concurrent processing of {len(file_paths)} files with {self.max_workers} workers")
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all files for processing
            future_to_file = {
                executor.submit(self.process_single_file_wrapper, file_path, progress_callback): file_path 
                for file_path in file_paths
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    result = future.result()
                    with self.results_lock:
                        results.append(result)
                    logger.info(f"Completed processing: {result.filename} - Status: {result.status}")
                except Exception as e:
                    filename = os.path.basename(file_path)
                    error_result = FileProcessingResult(
                        filename=filename,
                        file_path=file_path,
                        status='failed',
                        error_message=f"Future execution error: {str(e)}",
                        processing_time=0
                    )
                    with self.results_lock:
                        results.append(error_result)
                    logger.error(f"Future execution error for {filename}: {e}")
        
        # Calculate summary statistics
        total_processing_time = time.time() - start_time
        successful_files = sum(1 for r in results if r.status == 'success')
        failed_files = sum(1 for r in results if r.status == 'failed')
        
        # Sort results by filename for consistent ordering
        results.sort(key=lambda x: x.filename)
        
        multi_result = MultiFileProcessingResult(
            total_files=len(file_paths),
            successful_files=successful_files,
            failed_files=failed_files,
            total_processing_time=total_processing_time,
            results=results
        )
        
        logger.info(f"Multi-file processing completed. Success: {successful_files}, Failed: {failed_files}, "
                   f"Total time: {total_processing_time:.2f}s")
        
        return multi_result


def process_multiple_files(file_paths: List[str], 
                          max_workers: int = 5, 
                          progress_callback: Optional[Callable] = None) -> MultiFileProcessingResult:
    """
    Convenience function to process multiple files with default settings.
    
    Args:
        file_paths: List of file paths to process
        max_workers: Maximum number of concurrent threads (default: 5)
        progress_callback: Optional callback function for progress updates
        
    Returns:
        MultiFileProcessingResult object with aggregated results
    """
    processor = MultiFileProcessor(max_workers=max_workers)
    return processor.process_multiple_files(file_paths, progress_callback)


def get_pdf_files_from_folder(folder_path: str) -> List[str]:
    """
    Get all PDF files from a folder and its subfolders.
    
    Args:
        folder_path: Path to the folder to search
        
    Returns:
        List of PDF file paths
    """
    pdf_files = []
    folder_path = Path(folder_path)
    
    if folder_path.exists():
        pdf_files = list(folder_path.rglob("*.pdf"))
        pdf_files = [str(pdf_file) for pdf_file in pdf_files]
        
    logger.info(f"Found {len(pdf_files)} PDF files in {folder_path}")
    return pdf_files
