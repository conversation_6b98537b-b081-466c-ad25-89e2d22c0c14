import difflib

import pandas as pd
from langchain_core.messages import HumanMessage, SystemMessage

from Constants.Config import settings
from Constants.PolicyFields import POLICY_FIELD_MAPPINGS, forms_and_endorsements
from Constants.Prompts import human_prompt_field_extraction, system_prompt_field_extraction
from Constants.SearchTerms import queries_per_product
from Dal.VectorStoreDal import retrieve_context_hybrid, sort_and_deduplicate_chunks
from OCR.Pdf_Text_Extractor import pdf_to_images_base64
from utils.DataUtils import format_to_json, limit_chunks_by_max_tokens, enc, format_output_excel
from utils.LogUtils import logger


def extract_key_fields(llm_prediction:str,llm, document_path,send_pdf=True) -> dict:
    """
    Extract key fields based on LLM prediction matching POLICY_FIELD_MAPPINGS.

    Args:
        llm_prediction: The predicted policy type from LLM
    Returns:
        Dictionary containing extraction results or field information
    """
    global output_path
    logger.info(f"Checking prediction '{llm_prediction}' against POLICY_FIELD_MAPPINGS...")
    # Step 1: Find matching policy type
    best_match = None
    best_similarity = 0.0
    similarity_threshold = 0.7
    result = {}

    for policy_type in POLICY_FIELD_MAPPINGS.keys():
        similarity = difflib.SequenceMatcher(None, llm_prediction.strip().lower(), policy_type.lower()).ratio()
        if similarity > best_similarity:
            best_similarity = similarity
            best_match = policy_type

    # Step 2: Check if we found a good match
    if best_similarity < similarity_threshold:
        logger.warning(f"No matching policy type found. Best match: '{best_match}' (similarity: {best_similarity:.3f})")
        return {
            "status": "Failed",
            "policy_type": llm_prediction,
            "result": None,
            "output_file_path": None
        }

    logger.info(f"Found matching policy: '{best_match}' (similarity: {best_similarity:.3f})")

    # Step 3: Setup Context for LLM
    fields_to_extract = POLICY_FIELD_MAPPINGS[best_match]
    completed_fields = {}

    #for key, value in fields_to_extract.items():
    if True:
        key = "complete"
        logger.info(f" CATEGORY: {key}")
        queries_for_context = [term for terms in queries_per_product[best_match].values() for term in terms]
        #queries_for_context = queries_per_product[best_match][key]
        comprehensive_context = []
        logger.info("🔍 Retrieving context from vector database...")
        logger.info("📚 Using Hybrid Retrieval approach (Vector + BM25):")
        for q_idx, primary_query in enumerate(queries_for_context):
            logger.info(f"Querying with hybrid retrieval: \"{primary_query[:50]}...\"")
            retrieved_docs = retrieve_context_hybrid.invoke(primary_query,vector_weight=0.6, bm25_weight=0.4)
            comprehensive_context.extend(retrieved_docs)
        comprehensive_context = sort_and_deduplicate_chunks(comprehensive_context)
        filtered_context = limit_chunks_by_max_tokens(comprehensive_context, settings.max_context_extraction)

        if key == "forms_and_endorsements":
            filtered_context = [doc for doc in filtered_context if any(term in doc.page_content for term in forms_and_endorsements)]

    # Step 4: Prepare Data for LLM
        context_parts = []
        for doc in filtered_context:
            page_num = doc.metadata.get('page_number', 'Unknown')
            content = doc.page_content.strip()
            context_parts.append(f"[File Name {doc.metadata.get('source', 'Unknown')}, Page {page_num}]: {content}")
            logger.info(f"[File Name {doc.metadata.get('source', 'Unknown')}, Page {page_num}]")
        final_context = "\n\n".join(context_parts)

        logger.info(f"Final context length: {len(enc.encode(final_context))} Tokens")

    # Step 5: Prepare Prompt
        human_prompt = human_prompt_field_extraction.format(best_match=best_match, context=final_context,fields_list=str(fields_to_extract))
        system_prompt = system_prompt_field_extraction
        human_message = [{"type": "text", "text": human_prompt}]
        if send_pdf: # Upload PDF pages to Model
            for doc in filtered_context:
                data_url = pdf_to_images_base64(doc.metadata['file_path'],page_number=doc.metadata['page_number'])
                human_message.append({"type": "image_url","image_url": {"url": f"data:image/png;base64,{data_url}"}})

        chat_history = [SystemMessage(content=system_prompt), HumanMessage(content=human_message)]

    # Step 6: LLM call for Field Extraction
        max_retries = 2
        for attempt in range(max_retries):
            try:
                logger.info(f"Field extraction attempt {attempt + 1}/{max_retries}")
                raw_response = llm.invoke(chat_history)
                raw_content = raw_response.content if hasattr(raw_response, 'content') else str(raw_response)
                logger.info(f"Response : {raw_content}\nResponse Metadata: {raw_response.response_metadata['token_usage']}")
                output, output_path = post_process_fields(raw_content,document_path,policy_type=best_match)
                if output:
                    result = {
                        "status": "Success",
                        "policy_type": best_match,
                        "result": output,
                        "output_file_path": output_path
                    }
                    break
                else:
                    logger.warning(f"Failed to extract fields. Retrying...")
                    continue
            except Exception as e:
                logger.error(f"Error in attempt {attempt + 1}: {str(e)}")
                if attempt < max_retries - 1:
                    continue

    return result


def post_process_fields(raw_content, document_path, policy_type):
    try:
        extraction_result, is_json = format_to_json(raw_content)
        filename = document_path.stem
        forms_and_endorsements_df = pd.DataFrame()
        underlying_details_df = pd.DataFrame()

        # Create DataFrames without modifying the original JSON
        if "Schedule of Forms and Endorsements" in extraction_result:
            forms_and_endorsements_df = pd.DataFrame(
                extraction_result.get("Schedule of Forms and Endorsements", [])
            )

        if "underlying_details" in extraction_result:
            underlying_details_df = pd.DataFrame(
                extraction_result.get("underlying_details", [])
            )

        # Create a copy of extraction_result for display in DataFrame
        display_result = extraction_result.copy()

        if not forms_and_endorsements_df.empty:
            display_result["Schedule of Forms and Endorsements"] = "Refer Sheet - Forms & Endorsements"

        if not underlying_details_df.empty:
            display_result["underlying_details"] = "Refer Sheet - Coverages & Limits"

        # Create the main output DataFrame
        if display_result:
            logger.info(f"Field Extraction Result: {display_result}")
            main_df = pd.DataFrame(list(display_result.items()), columns=["Headers", "Outputs"])
        else:
            main_df = pd.DataFrame(columns=["Headers", "Outputs"])

        output_path = f"data/outputs/output_{filename}.xlsx"

        # Write all DataFrames to respective sheets
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            main_df.to_excel(writer, index=False, sheet_name='Field Extraction')
            if not forms_and_endorsements_df.empty:
                forms_and_endorsements_df.to_excel(writer, index=False, sheet_name='Forms & Endorsements')
            if not underlying_details_df.empty:
                underlying_details_df.to_excel(writer, index=False, sheet_name='Coverages & Limits')

        # Format the Excel output
        format_output_excel(output_path)

    except Exception as e:
        logger.error(f"Error formatting to JSON: {str(e)}")
        return {}, False
    else:
        return extraction_result, output_path
